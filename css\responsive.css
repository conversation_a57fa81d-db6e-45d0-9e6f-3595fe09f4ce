/* ===== Responsive Design ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-title .name {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-content .container {
        gap: var(--spacing-2xl);
    }
    
    .about-content {
        gap: var(--spacing-2xl);
    }
    
    .contact-content {
        gap: var(--spacing-2xl);
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: var(--spacing-2xl) 0;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }
    
    .nav-menu.active {
        right: 0;
    }
    
    .nav-item {
        margin: var(--spacing-sm) 0;
    }
    
    .nav-link {
        padding: var(--spacing-lg) var(--spacing-xl);
        font-size: var(--font-size-lg);
        border-radius: var(--radius-lg);
        width: 200px;
        justify-content: center;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hamburger.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Hero Section */
    .hero-content .container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-2xl);
    }
    
    .hero-title .name {
        font-size: var(--font-size-4xl);
    }
    
    .hero-title .slogan {
        font-size: var(--font-size-xl);
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-img {
        max-width: 300px;
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* About */
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .about-quote {
        position: static;
        margin-top: var(--spacing-lg);
        max-width: none;
    }
    
    .vision-points {
        grid-template-columns: 1fr;
    }
    
    /* Program */
    .program-tabs {
        gap: var(--spacing-xs);
    }
    
    .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .tab-btn i {
        display: none;
    }
    
    /* Achievements */
    .achievements-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    /* Gallery */
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    /* Join */
    .join-intro {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .volunteer-types {
        grid-template-columns: 1fr;
    }
    
    /* Contact */
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .social-links {
        gap: var(--spacing-sm);
    }
    
    .social-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    /* Typography */
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    /* Hero */
    .hero {
        min-height: 80vh;
    }
    
    .hero-title .name {
        font-size: var(--font-size-3xl);
    }
    
    .hero-title .slogan {
        font-size: var(--font-size-lg);
    }
    
    .hero-description {
        font-size: var(--font-size-base);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .stat-item {
        padding: var(--spacing-lg);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-md);
    }
    
    .stat-icon i {
        font-size: var(--font-size-2xl);
    }
    
    .stat-number {
        font-size: var(--font-size-3xl);
    }
    
    /* Program */
    .program-tabs {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .tab-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    .tab-panel {
        padding: var(--spacing-xl);
    }
    
    .program-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .program-item i {
        font-size: var(--font-size-3xl);
    }
    
    /* Gallery */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .gallery-img {
        height: 200px;
    }
    
    /* Forms */
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .volunteer-form-container,
    .contact-form-container {
        padding: var(--spacing-xl);
    }
    
    /* Contact */
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .contact-icon {
        margin: 0 auto;
    }
    
    .social-links {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .social-link {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .footer-social {
        justify-content: center;
    }
    
    /* Modals */
    .lightbox-content,
    .video-modal-content {
        max-width: 95%;
        max-height: 80%;
    }
    
    .lightbox-close,
    .video-modal-close {
        top: -30px;
        font-size: var(--font-size-2xl);
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    /* Base */
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .section {
        padding: var(--spacing-2xl) 0;
    }
    
    /* Navigation */
    .nav-container {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .logo-text {
        font-size: var(--font-size-lg);
    }
    
    .nav-menu {
        top: 60px;
        height: calc(100vh - 60px);
        padding: var(--spacing-xl) 0;
    }
    
    .nav-link {
        width: 180px;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    /* Typography */
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    /* Hero */
    .hero-content {
        padding: var(--spacing-2xl) 0;
    }
    
    .hero-title .name {
        font-size: var(--font-size-2xl);
    }
    
    .hero-title .slogan {
        font-size: var(--font-size-base);
    }
    
    .hero-description {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-xl);
    }
    
    .hero-img {
        max-width: 250px;
    }
    
    /* Quick Stats */
    .quick-stats {
        padding: var(--spacing-2xl) 0;
    }
    
    .stat-item {
        padding: var(--spacing-md);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        margin-bottom: var(--spacing-sm);
    }
    
    .stat-icon i {
        font-size: var(--font-size-xl);
    }
    
    .stat-number {
        font-size: var(--font-size-2xl);
    }
    
    .stat-label {
        font-size: var(--font-size-base);
    }
    
    /* About */
    .about-text h3 {
        font-size: var(--font-size-lg);
    }
    
    .about-quote {
        padding: var(--spacing-md);
    }
    
    .about-quote i {
        font-size: var(--font-size-xl);
    }
    
    .about-quote p {
        font-size: var(--font-size-xs);
    }
    
    /* Program */
    .tab-btn {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
        max-width: 200px;
    }
    
    .tab-panel {
        padding: var(--spacing-lg);
    }
    
    .tab-panel h3 {
        font-size: var(--font-size-xl);
    }
    
    .program-item {
        padding: var(--spacing-md);
    }
    
    .program-item i {
        font-size: var(--font-size-2xl);
    }
    
    .program-item h4 {
        font-size: var(--font-size-base);
    }
    
    .program-item p {
        font-size: var(--font-size-sm);
    }
    
    /* Achievements */
    .achievements-filter {
        gap: var(--spacing-xs);
    }
    
    .filter-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    .achievement-card {
        margin-bottom: var(--spacing-md);
    }
    
    .achievement-icon {
        width: 60px;
        height: 60px;
        margin: var(--spacing-lg) auto var(--spacing-md);
    }
    
    .achievement-icon i {
        font-size: var(--font-size-xl);
    }
    
    .achievement-content {
        padding: 0 var(--spacing-md) var(--spacing-lg);
    }
    
    .achievement-content h3 {
        font-size: var(--font-size-base);
    }
    
    .achievement-description {
        font-size: var(--font-size-sm);
    }
    
    .achievement-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    /* Gallery */
    .gallery-filter {
        gap: var(--spacing-xs);
    }
    
    .gallery-img {
        height: 180px;
    }
    
    .gallery-content h3 {
        font-size: var(--font-size-base);
    }
    
    .gallery-content p {
        font-size: var(--font-size-xs);
    }
    
    .gallery-btn {
        width: 40px;
        height: 40px;
    }
    
    /* Forms */
    .volunteer-form-container,
    .contact-form-container {
        padding: var(--spacing-lg);
    }
    
    .volunteer-form h3,
    .contact-form h3 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-xl);
    }
    
    .form-group label {
        font-size: var(--font-size-sm);
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .btn-submit {
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }
    
    /* Join */
    .join-text h3 {
        font-size: var(--font-size-xl);
    }
    
    .benefit-item {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .benefit-item i {
        font-size: var(--font-size-base);
    }
    
    .benefit-item span {
        font-size: var(--font-size-sm);
    }
    
    /* Contact */
    .contact-item {
        padding: var(--spacing-lg);
    }
    
    .contact-icon {
        width: 50px;
        height: 50px;
    }
    
    .contact-icon i {
        font-size: var(--font-size-lg);
    }
    
    .contact-details h3 {
        font-size: var(--font-size-base);
    }
    
    .contact-details p {
        font-size: var(--font-size-sm);
    }
    
    .social-media h3 {
        font-size: var(--font-size-xl);
    }
    
    .social-link {
        padding: var(--spacing-sm);
        font-size: var(--font-size-xs);
        max-width: 150px;
    }
    
    .map-placeholder {
        padding: var(--spacing-xl);
    }
    
    .map-placeholder i {
        font-size: var(--font-size-4xl);
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-2xl) 0 var(--spacing-lg);
    }
    
    .footer-content {
        gap: var(--spacing-lg);
    }
    
    .footer-section h4 {
        font-size: var(--font-size-base);
    }
    
    .footer-logo-img {
        width: 50px;
        height: 50px;
    }
    
    .footer-logo h3 {
        font-size: var(--font-size-lg);
    }
    
    .footer-logo p {
        font-size: var(--font-size-sm);
    }
    
    .footer-links a,
    .footer-contact li {
        font-size: var(--font-size-sm);
    }
    
    .social-icon {
        width: 35px;
        height: 35px;
    }
    
    .footer-bottom {
        padding-top: var(--spacing-lg);
    }
    
    .footer-bottom p {
        font-size: var(--font-size-sm);
    }
    
    /* Back to Top */
    .back-to-top {
        width: 45px;
        height: 45px;
        bottom: var(--spacing-md);
        left: var(--spacing-md);
    }
    
    /* Success Message */
    .success-message {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
    }
}

/* Mobile Extra Small (up to 375px) */
@media (max-width: 375px) {
    /* Base */
    .container {
        padding: 0 var(--spacing-xs);
    }
    
    /* Navigation */
    .nav-container {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .logo-img {
        width: 35px;
        height: 35px;
    }
    
    .logo-text {
        font-size: var(--font-size-base);
    }
    
    /* Hero */
    .hero-title .name {
        font-size: var(--font-size-xl);
    }
    
    .hero-title .slogan {
        font-size: var(--font-size-sm);
    }
    
    .hero-description {
        font-size: var(--font-size-xs);
    }
    
    .hero-img {
        max-width: 200px;
    }
    
    /* Buttons */
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    /* Stats */
    .stat-item {
        padding: var(--spacing-sm);
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
    }
    
    .stat-icon i {
        font-size: var(--font-size-lg);
    }
    
    .stat-number {
        font-size: var(--font-size-xl);
    }
    
    .stat-label {
        font-size: var(--font-size-sm);
    }
    
    /* Program */
    .tab-btn {
        max-width: 150px;
        padding: var(--spacing-xs);
        font-size: 10px;
    }
    
    /* Gallery */
    .gallery-img {
        height: 150px;
    }
    
    /* Forms */
    .volunteer-form-container,
    .contact-form-container {
        padding: var(--spacing-md);
    }
    
    /* Contact */
    .contact-item {
        padding: var(--spacing-md);
    }
    
    .contact-icon {
        width: 40px;
        height: 40px;
    }
    
    .social-link {
        max-width: 120px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    /* Footer */
    .footer-content {
        gap: var(--spacing-md);
    }
    
    .social-icon {
        width: 30px;
        height: 30px;
    }
}

/* Print Styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .navbar,
    .hamburger,
    .back-to-top,
    .success-message,
    .lightbox,
    .video-modal {
        display: none !important;
    }
    
    .section {
        display: block !important;
        page-break-inside: avoid;
    }
    
    .hero {
        min-height: auto;
        background: none !important;
    }
    
    .hero-content {
        color: black !important;
    }
    
    .btn {
        border: 1px solid black !important;
    }
    
    a {
        text-decoration: underline;
    }
    
    .footer {
        page-break-before: always;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #006400;
        --white: #ffffff;
        --black: #000000;
        --gray: #666666;
        --dark-gray: #333333;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
    
    .nav-link:hover,
    .nav-link.active {
        background: var(--black);
        color: var(--white);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .scroll-arrow {
        animation: none;
    }
    
    .loader-circle {
        animation: none;
        border: 4px solid var(--primary-color);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a1a1a;
        --light-gray: #2d2d2d;
        --gray: #a0a0a0;
        --dark-gray: #e0e0e0;
        --black: #ffffff;
    }
    
    body {
        background-color: var(--white);
        color: var(--dark-gray);
    }
    
    .navbar {
        background: rgba(26, 26, 26, 0.95);
    }
    
    .section {
        background: var(--white);
    }
    
    #about,
    #join {
        background: var(--light-gray);
    }
    
    #program,
    #gallery,
    #contact {
        background: var(--white);
    }
}

/* ===== Hero Profile Responsive Styles ===== */

/* Large screens */
@media (min-width: 1200px) {
    .hero-profile,
    .hero-profile-placeholder {
        width: 320px;
        height: 320px;
        font-size: 140px;
    }
}

/* Medium screens */
@media (max-width: 992px) {
    .hero-profile,
    .hero-profile-placeholder {
        width: 250px;
        height: 250px;
        font-size: 100px;
        border-width: 5px;
    }
}

/* Small screens */
@media (max-width: 768px) {
    .hero-profile,
    .hero-profile-placeholder {
        width: 200px;
        height: 200px;
        font-size: 80px;
        border-width: 4px;
        margin-bottom: 1.5rem;
    }
    
    .hero-profile-placeholder::after {
        font-size: 12px;
        bottom: -35px;
        padding: 6px 12px;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .hero-profile,
    .hero-profile-placeholder {
        width: 160px;
        height: 160px;
        font-size: 60px;
        border-width: 3px;
        margin-bottom: 1rem;
    }
    
    .hero-profile-placeholder::after {
        font-size: 11px;
        bottom: -30px;
        padding: 4px 8px;
    }
}