// ===== Animations JavaScript File =====

// Animation Configuration
const animationConfig = {
    duration: {
        fast: 300,
        normal: 500,
        slow: 800
    },
    easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }
};

// ===== Intersection Observer for Scroll Animations =====
class ScrollAnimator {
    constructor() {
        this.observers = new Map();
        this.init();
    }
    
    init() {
        this.createObservers();
        this.observeElements();
    }
    
    createObservers() {
        // Fade In Observer
        this.observers.set('fadeIn', new IntersectionObserver(
            this.handleFadeIn.bind(this),
            {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            }
        ));
        
        // Slide Up Observer
        this.observers.set('slideUp', new IntersectionObserver(
            this.handleSlideUp.bind(this),
            {
                threshold: 0.2,
                rootMargin: '0px 0px -100px 0px'
            }
        ));
        
        // Scale In Observer
        this.observers.set('scaleIn', new IntersectionObserver(
            this.handleScaleIn.bind(this),
            {
                threshold: 0.3,
                rootMargin: '0px 0px -50px 0px'
            }
        ));
        
        // Stagger Observer
        this.observers.set('stagger', new IntersectionObserver(
            this.handleStagger.bind(this),
            {
                threshold: 0.1,
                rootMargin: '0px 0px -100px 0px'
            }
        ));
    }
    
    observeElements() {
        // Fade In Elements
        document.querySelectorAll('[data-animate="fade-in"]').forEach(el => {
            this.observers.get('fadeIn').observe(el);
        });
        
        // Slide Up Elements
        document.querySelectorAll('[data-animate="slide-up"]').forEach(el => {
            this.observers.get('slideUp').observe(el);
        });
        
        // Scale In Elements
        document.querySelectorAll('[data-animate="scale-in"]').forEach(el => {
            this.observers.get('scaleIn').observe(el);
        });
        
        // Stagger Elements
        document.querySelectorAll('[data-animate="stagger"]').forEach(el => {
            this.observers.get('stagger').observe(el);
        });
        
        // Auto-detect elements for animation
        this.autoDetectElements();
    }
    
    autoDetectElements() {
        // Automatically add animations to common elements
        const elementsToAnimate = [
            { selector: '.stat-item', animation: 'scale-in' },
            { selector: '.achievement-card', animation: 'fade-in' },
            { selector: '.gallery-item', animation: 'scale-in' },
            { selector: '.program-item', animation: 'slide-up' },
            { selector: '.contact-item', animation: 'fade-in' },
            { selector: '.benefit-item', animation: 'slide-up' }
        ];
        
        elementsToAnimate.forEach(({ selector, animation }) => {
            document.querySelectorAll(selector).forEach(el => {
                if (!el.hasAttribute('data-animate')) {
                    el.setAttribute('data-animate', animation);
                    this.observers.get(this.getObserverKey(animation))?.observe(el);
                }
            });
        });
    }
    
    getObserverKey(animation) {
        const mapping = {
            'fade-in': 'fadeIn',
            'slide-up': 'slideUp',
            'scale-in': 'scaleIn',
            'stagger': 'stagger'
        };
        return mapping[animation] || 'fadeIn';
    }
    
    handleFadeIn(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.animateFadeIn(entry.target);
                this.observers.get('fadeIn').unobserve(entry.target);
            }
        });
    }
    
    handleSlideUp(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.animateSlideUp(entry.target);
                this.observers.get('slideUp').unobserve(entry.target);
            }
        });
    }
    
    handleScaleIn(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.animateScaleIn(entry.target);
                this.observers.get('scaleIn').unobserve(entry.target);
            }
        });
    }
    
    handleStagger(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.animateStagger(entry.target);
                this.observers.get('stagger').unobserve(entry.target);
            }
        });
    }
    
    animateFadeIn(element) {
        element.style.opacity = '0';
        element.style.transition = `opacity ${animationConfig.duration.normal}ms ${animationConfig.easing.easeOut}`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
        });
    }
    
    animateSlideUp(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = `all ${animationConfig.duration.normal}ms ${animationConfig.easing.easeOut}`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        });
    }
    
    animateScaleIn(element) {
        element.style.opacity = '0';
        element.style.transform = 'scale(0.8)';
        element.style.transition = `all ${animationConfig.duration.normal}ms ${animationConfig.easing.bounce}`;
        
        requestAnimationFrame(() => {
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
        });
    }
    
    animateStagger(container) {
        const children = container.children;
        Array.from(children).forEach((child, index) => {
            child.style.opacity = '0';
            child.style.transform = 'translateY(20px)';
            child.style.transition = `all ${animationConfig.duration.normal}ms ${animationConfig.easing.easeOut}`;
            
            setTimeout(() => {
                child.style.opacity = '1';
                child.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
}

// ===== Typing Animation =====
class TypingAnimation {
    constructor(element, texts, options = {}) {
        this.element = element;
        this.texts = Array.isArray(texts) ? texts : [texts];
        this.options = {
            typeSpeed: options.typeSpeed || 100,
            deleteSpeed: options.deleteSpeed || 50,
            pauseTime: options.pauseTime || 2000,
            loop: options.loop !== false,
            cursor: options.cursor || '|'
        };
        this.currentTextIndex = 0;
        this.currentCharIndex = 0;
        this.isDeleting = false;
        this.isTyping = false;
        
        this.init();
    }
    
    init() {
        this.element.innerHTML = this.options.cursor;
        this.startTyping();
    }
    
    startTyping() {
        if (this.isTyping) return;
        this.isTyping = true;
        this.type();
    }
    
    type() {
        const currentText = this.texts[this.currentTextIndex];
        
        if (!this.isDeleting) {
            // Typing
            if (this.currentCharIndex < currentText.length) {
                this.element.innerHTML = currentText.substring(0, this.currentCharIndex + 1) + this.options.cursor;
                this.currentCharIndex++;
                setTimeout(() => this.type(), this.options.typeSpeed);
            } else {
                // Finished typing, pause then start deleting
                setTimeout(() => {
                    this.isDeleting = true;
                    this.type();
                }, this.options.pauseTime);
            }
        } else {
            // Deleting
            if (this.currentCharIndex > 0) {
                this.element.innerHTML = currentText.substring(0, this.currentCharIndex - 1) + this.options.cursor;
                this.currentCharIndex--;
                setTimeout(() => this.type(), this.options.deleteSpeed);
            } else {
                // Finished deleting, move to next text
                this.isDeleting = false;
                this.currentTextIndex = (this.currentTextIndex + 1) % this.texts.length;
                
                if (this.options.loop || this.currentTextIndex !== 0) {
                    setTimeout(() => this.type(), this.options.typeSpeed);
                } else {
                    this.isTyping = false;
                }
            }
        }
    }
    
    stop() {
        this.isTyping = false;
    }
    
    restart() {
        this.currentTextIndex = 0;
        this.currentCharIndex = 0;
        this.isDeleting = false;
        this.startTyping();
    }
}

// ===== Particle Animation =====
class ParticleSystem {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.particles = [];
        this.options = {
            particleCount: options.particleCount || 50,
            particleSize: options.particleSize || 2,
            particleSpeed: options.particleSpeed || 1,
            particleColor: options.particleColor || '#1e3a8a',
            connectionDistance: options.connectionDistance || 100,
            ...options
        };
        
        this.init();
    }
    
    init() {
        this.resizeCanvas();
        this.createParticles();
        this.animate();
        
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    resizeCanvas() {
        this.canvas.width = this.canvas.offsetWidth;
        this.canvas.height = this.canvas.offsetHeight;
    }
    
    createParticles() {
        this.particles = [];
        for (let i = 0; i < this.options.particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * this.options.particleSpeed,
                vy: (Math.random() - 0.5) * this.options.particleSpeed,
                size: Math.random() * this.options.particleSize + 1
            });
        }
    }
    
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Update and draw particles
        this.particles.forEach(particle => {
            // Update position
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // Bounce off edges
            if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;
            
            // Draw particle
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fillStyle = this.options.particleColor;
            this.ctx.fill();
        });
        
        // Draw connections
        this.drawConnections();
        
        requestAnimationFrame(() => this.animate());
    }
    
    drawConnections() {
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                const dx = this.particles[i].x - this.particles[j].x;
                const dy = this.particles[i].y - this.particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < this.options.connectionDistance) {
                    const opacity = 1 - (distance / this.options.connectionDistance);
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
                    this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
                    this.ctx.strokeStyle = `rgba(30, 58, 138, ${opacity * 0.3})`;
                    this.ctx.lineWidth = 1;
                    this.ctx.stroke();
                }
            }
        }
    }
}

// ===== Morphing Animation =====
class MorphingAnimation {
    constructor(element, shapes, options = {}) {
        this.element = element;
        this.shapes = shapes;
        this.options = {
            duration: options.duration || 2000,
            easing: options.easing || animationConfig.easing.easeInOut,
            loop: options.loop !== false,
            ...options
        };
        this.currentShapeIndex = 0;
        
        this.init();
    }
    
    init() {
        if (this.shapes.length > 1) {
            this.startMorphing();
        }
    }
    
    startMorphing() {
        const nextIndex = (this.currentShapeIndex + 1) % this.shapes.length;
        const currentShape = this.shapes[this.currentShapeIndex];
        const nextShape = this.shapes[nextIndex];
        
        this.element.style.transition = `all ${this.options.duration}ms ${this.options.easing}`;
        
        // Apply next shape properties
        Object.keys(nextShape).forEach(property => {
            this.element.style[property] = nextShape[property];
        });
        
        this.currentShapeIndex = nextIndex;
        
        if (this.options.loop) {
            setTimeout(() => this.startMorphing(), this.options.duration + 500);
        }
    }
}

// ===== Loading Animation =====
class LoadingAnimation {
    constructor(element, type = 'spinner') {
        this.element = element;
        this.type = type;
        this.isActive = false;
        
        this.animations = {
            spinner: this.createSpinner.bind(this),
            dots: this.createDots.bind(this),
            pulse: this.createPulse.bind(this),
            wave: this.createWave.bind(this)
        };
    }
    
    start() {
        if (this.isActive) return;
        this.isActive = true;
        this.animations[this.type]();
    }
    
    stop() {
        this.isActive = false;
        this.element.innerHTML = '';
    }
    
    createSpinner() {
        this.element.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner-circle"></div>
            </div>
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .loading-spinner {
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .spinner-circle {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #1e3a8a;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }
    
    createDots() {
        this.element.innerHTML = `
            <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .loading-dots {
                display: flex;
                gap: 5px;
                justify-content: center;
                align-items: center;
            }
            .dot {
                width: 10px;
                height: 10px;
                background: #1e3a8a;
                border-radius: 50%;
                animation: dotPulse 1.4s ease-in-out infinite both;
            }
            .dot:nth-child(1) { animation-delay: -0.32s; }
            .dot:nth-child(2) { animation-delay: -0.16s; }
            @keyframes dotPulse {
                0%, 80%, 100% { transform: scale(0); }
                40% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    }
    
    createPulse() {
        this.element.innerHTML = `
            <div class="loading-pulse">
                <div class="pulse-circle"></div>
            </div>
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .loading-pulse {
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .pulse-circle {
                width: 40px;
                height: 40px;
                background: #1e3a8a;
                border-radius: 50%;
                animation: pulse 2s ease-in-out infinite;
            }
            @keyframes pulse {
                0% { transform: scale(0); opacity: 1; }
                100% { transform: scale(1); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    createWave() {
        this.element.innerHTML = `
            <div class="loading-wave">
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
            </div>
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .loading-wave {
                display: flex;
                gap: 3px;
                justify-content: center;
                align-items: center;
            }
            .wave-bar {
                width: 4px;
                height: 20px;
                background: #1e3a8a;
                animation: wave 1.2s ease-in-out infinite;
            }
            .wave-bar:nth-child(1) { animation-delay: -1.2s; }
            .wave-bar:nth-child(2) { animation-delay: -1.1s; }
            .wave-bar:nth-child(3) { animation-delay: -1.0s; }
            .wave-bar:nth-child(4) { animation-delay: -0.9s; }
            .wave-bar:nth-child(5) { animation-delay: -0.8s; }
            @keyframes wave {
                0%, 40%, 100% { transform: scaleY(0.4); }
                20% { transform: scaleY(1.0); }
            }
        `;
        document.head.appendChild(style);
    }
}

// ===== Initialize Animations =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize scroll animator
    const scrollAnimator = new ScrollAnimator();
    
    // Initialize typing animation for hero slogan (if exists)
    const heroSlogan = document.querySelector('.hero-title .slogan');
    if (heroSlogan) {
        const slogans = [
            'صوتٌ للعطاء، وعدٌ للتغيير',
            'معاً نبني مستقبل أفضل',
            'خدمة المواطن أولويتنا',
            'نزاهة وشفافية في العمل'
        ];
        
        // Start typing animation after a delay
        setTimeout(() => {
            new TypingAnimation(heroSlogan, slogans, {
                typeSpeed: 80,
                deleteSpeed: 40,
                pauseTime: 3000
            });
        }, 2000);
    }
    
    // Initialize particle system for hero background (if canvas exists)
    const particleCanvas = document.querySelector('#particle-canvas');
    if (particleCanvas) {
        new ParticleSystem(particleCanvas, {
            particleCount: 30,
            particleSize: 3,
            particleSpeed: 0.5,
            particleColor: 'rgba(255, 255, 255, 0.6)',
            connectionDistance: 120
        });
    }
    
    // Add hover animations to cards
    const cards = document.querySelectorAll('.stat-item, .achievement-card, .gallery-item, .contact-item');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add CSS for ripple animation
    const rippleStyle = document.createElement('style');
    rippleStyle.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(rippleStyle);
});

// ===== Export Animation Classes =====
window.AnimationUtils = {
    ScrollAnimator,
    TypingAnimation,
    ParticleSystem,
    MorphingAnimation,
    LoadingAnimation,
    animationConfig
};