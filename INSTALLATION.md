# دليل التثبيت والتشغيل - موقع الأستاذ رحيم مجيسر

## 🚀 طرق التشغيل

### 1. التشغيل المباشر (الأسهل)
```bash
# افتح الملف مباشرة في المتصفح
# انقر نقراً مزدوجاً على index.html
```

### 2. خادم محلي بسيط

#### باستخدام Python (مثبت افتراضياً على معظم الأنظمة)
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# ثم افتح المتصفح على: http://localhost:8000
```

#### باستخدام Node.js
```bash
# تثبيت http-server عالمياً
npm install -g http-server

# تشغيل الخادم
http-server -p 8000 -o

# أو استخدام npx بدون تثبيت
npx http-server -p 8000 -o
```

#### باستخدام PHP
```bash
php -S localhost:8000
```

### 3. استخدام Live Server (VS Code)
1. افتح المشروع في VS Code
2. ثبت إضافة "Live Server"
3. انقر بالزر الأيمن على index.html
4. اختر "Open with Live Server"

## 📁 هيكل المشروع

```
raheem-majiser-website/
├── index.html              # الصفحة الرئيسية
├── manifest.json           # ملف PWA
├── sw.js                   # Service Worker
├── robots.txt              # ملف محركات البحث
├── sitemap.xml             # خريطة الموقع
├── .htaccess              # إعدادات Apache
├── package.json           # إعدادات Node.js
├── README.md              # دليل المشروع
├── INSTALLATION.md        # هذا الملف
├── css/
│   ├── style.css          # الأنماط الرئيسية
│   └── responsive.css     # الأنماط المتجاوبة
├── js/
│   ├── main.js           # الوظائف الرئيسية
│   ├── animations.js     # الرسوم المتحركة
│   └── forms.js          # معالجة النماذج
├── images/
│   ├── logo.png          # شعار الحملة
│   ├── raheem-hero.jpg   # صورة رئيسية
│   ├── raheem-about.jpg  # صورة التعريف
│   ├── baghdad-bg.jpg    # خلفية بغداد
│   ├── volunteers.jpg    # صورة المتطوعين
│   ├── gallery/          # معرض الصور
│   └── icons/            # أيقونات PWA
└── videos/               # مقاطع الفيديو
```

## 🖼️ إضافة الصور

### الصور المطلوبة:
1. **logo.png** (200x200px) - شعار الحملة
2. **raheem-hero.jpg** (1200x800px) - صورة الصفحة الرئيسية
3. **raheem-about.jpg** (800x600px) - صورة صفحة التعريف
4. **baghdad-bg.jpg** (1920x1080px) - خلفية بغداد
5. **volunteers.jpg** (1000x600px) - صورة المتطوعين

### صور المعرض (في مجلد images/gallery/):
- **visit1.jpg** - زيارة للمناطق الشعبية
- **visit2.jpg** - زيارة مدرسة
- **charity1.jpg** - توزيع المساعدات
- **charity2.jpg** - افتتاح مستوصف
- **event1.jpg** - فعالية تعليمية
- **meeting1.jpg** - لقاء مع الشباب

### أيقونات PWA (في مجلد images/icons/):
- icon-16x16.png
- icon-32x32.png
- icon-72x72.png
- icon-96x96.png
- icon-128x128.png
- icon-144x144.png
- icon-152x152.png
- icon-192x192.png
- icon-384x384.png
- icon-512x512.png

## 🎥 إضافة الفيديوهات

ضع مقاطع الفيديو في مجلد `videos/`:
- **meeting1.mp4** - لقاء مع الشباب
- أي مقاطع فيديو أخرى

## ⚙️ التخصيص

### تغيير الألوان:
عدّل في ملف `css/style.css`:
```css
:root {
    --primary-color: #1e3a8a;    /* أزرق داكن */
    --secondary-color: #059669;   /* أخضر زيتوني */
    --accent-color: #f59e0b;      /* ذهبي */
}
```

### تغيير المحتوى:
عدّل النصوص في ملف `index.html`

### إضافة أقسام جديدة:
1. أضف القسم في `index.html`
2. أضف رابط في القائمة
3. أضف الأنماط في `css/style.css`

## 🌐 النشر على الإنترنت

### 1. استضافة مجانية:
- **GitHub Pages**: ارفع المشروع على GitHub وفعّل Pages
- **Netlify**: اسحب المجلد وأفلته على netlify.com
- **Vercel**: ربط مع GitHub أو رفع مباشر
- **Firebase Hosting**: استخدم Firebase CLI

### 2. استضافة مدفوعة:
- ارفع جميع الملفات إلى مجلد الجذر (public_html)
- تأكد من أن index.html في المجلد الرئيسي
- تأكد من دعم الاستضافة لملفات .htaccess

### خطوات النشر على cPanel:
1. اضغط الملفات في ملف ZIP
2. ارفع الملف إلى File Manager
3. استخرج الملفات في مجلد public_html
4. تأكد من الأذونات (755 للمجلدات، 644 للملفات)

## 🔧 أدوات التطوير (اختيارية)

### تثبيت Node.js وأدوات التطوير:
```bash
# تثبيت التبعيات
npm install

# تشغيل خادم التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# تحسين الصور
npm run optimize-images

# فحص الكود
npm run lint

# تنسيق الكود
npm run format

# اختبار الأداء
npm run lighthouse
```

## 📱 اختبار PWA

### على الهاتف:
1. افتح الموقع في Chrome أو Safari
2. اضغط على "إضافة إلى الشاشة الرئيسية"
3. اختبر العمل بدون إنترنت

### على سطح المكتب:
1. افتح الموقع في Chrome
2. ابحث عن أيقونة التثبيت في شريط العنوان
3. اضغط "تثبيت"

## 🐛 حل المشاكل الشائعة

### المشكلة: الصور لا تظهر
**الحل**: تأكد من وجود الصور في المجلدات الصحيحة ومن صحة أسماء الملفات

### المشكلة: الخطوط العربية لا تظهر بشكل صحيح
**الحل**: تأكد من اتصال الإنترنت لتحميل خطوط Google Fonts

### المشكلة: النماذج لا تعمل
**الحل**: تأكد من تشغيل الموقع على خادم (وليس فتح الملف مباشرة)

### المشكلة: Service Worker لا يعمل
**الحل**: تأكد من تشغيل الموقع على HTTPS أو localhost

### المشكلة: الموقع بطيء
**الحل**: 
- ضغط الصور
- تفعيل ضغط الملفات في الخادم
- استخدام CDN

## 📞 الدعم الفني

### للمساعدة:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964 790 123 4567
- **واتساب**: +964 780 123 4567

### الإبلاغ عن مشاكل:
- افتح issue في GitHub
- أرسل بريد إلكتروني مع تفاصيل المشكلة
- أرفق لقطة شاشة إن أمكن

## ✅ قائمة التحقق قبل النشر

- [ ] جميع الصور موجودة ومحسنة
- [ ] جميع الروابط تعمل بشكل صحيح
- [ ] النماذج تعمل وترسل البيانات
- [ ] الموقع متجاوب على جميع الأجهزة
- [ ] سرعة التحميل مقبولة
- [ ] SEO محسن (العنوان، الوصف، الكلمات المفتاحية)
- [ ] PWA يعمل بشكل صحيح
- [ ] اختبار على متصفحات مختلفة
- [ ] اختبار الوصول للمعاقين
- [ ] النسخ الاحتياطي جاهز

## 🔄 التحديثات المستقبلية

### المخطط لها:
- إضافة نظام إدارة محتوى
- تكامل مع قواعد البيانات
- نظام تعليقات
- دردشة مباشرة
- تحليلات متقدمة
- دعم لغات متعددة

### كيفية التحديث:
1. احتفظ بنسخة احتياطية
2. حمّل الإصدار الجديد
3. انسخ الصور والمحتوى المخصص
4. اختبر جميع الوظائف
5. انشر التحديث

---

**ملاحظة**: هذا الدليل يغطي الاستخدام الأساسي. للاستخدام المتقدم، راجع ملف README.md