/* ===== CSS Variables ===== */
:root {
    /* Colors */
    --primary-color: #1e3a8a;      /* أزرق داكن */
    --secondary-color: #059669;     /* أخضر زيتوني */
    --accent-color: #f59e0b;        /* ذهبي */
    --white: #ffffff;
    --light-gray: #f8fafc;
    --gray: #64748b;
    --dark-gray: #334155;
    --black: #0f172a;
    
    /* Gradients */
    --primary-gradient: linear-gradient(135deg, var(--primary-color), #3b82f6);
    --secondary-gradient: linear-gradient(135deg, var(--secondary-color), #10b981);
    --hero-gradient: linear-gradient(135deg, rgba(30, 58, 138, 0.9), rgba(59, 130, 246, 0.8));
    
    /* Typography */
    --font-family: 'Cairo', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== Reset & Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--black);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--secondary-color);
}

/* ===== Layout ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section {
    padding: var(--spacing-3xl) 0;
    display: none;
}

.section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 80px;
    height: 4px;
    background: var(--secondary-gradient);
    border-radius: var(--radius-sm);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== Loading Screen ===== */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loader {
    text-align: center;
}

.loader-circle {
    width: 50px;
    height: 50px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Navigation ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.logo-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--dark-gray);
    font-weight: 500;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(30, 58, 138, 0.1);
}

.nav-link i {
    font-size: var(--font-size-sm);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

/* ===== Buttons ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* ===== Hero Section ===== */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.9), rgba(59, 130, 246, 0.8)),
                url('../images/baghdad-bg.jpg') center/cover;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--hero-gradient);
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    width: 100%;
    padding: var(--spacing-3xl) 0;
}

.hero-content .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-text {
    color: var(--white);
}

.hero-title {
    margin-bottom: var(--spacing-xl);
}

.hero-title .name {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: 800;
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(45deg, #ffffff, #f1f5f9);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-title .slogan {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 400;
    color: #e2e8f0;
}

.hero-description {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-2xl);
    color: #e2e8f0;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-image {
    text-align: center;
}

.hero-img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    border: 4px solid rgba(255, 255, 255, 0.2);
}

/* Hero Profile Image */
.hero-profile {
    width: 320px;
    height: 320px;
    border-radius: 50%;
    margin: 0 auto 2rem;
    border: 8px solid rgba(245, 158, 11, 0.8); /* إطار ذهبي */
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.3),
        0 0 0 4px rgba(255, 255, 255, 0.2),
        inset 0 0 0 2px rgba(245, 158, 11, 0.3);
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.hero-profile::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: conic-gradient(from 0deg, var(--accent-color), var(--primary-color), var(--secondary-color), var(--accent-color));
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hero-profile img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top;
    transition: transform 0.4s ease;
    filter: brightness(1.05) contrast(1.1) saturate(1.1);
}

.hero-profile:hover {
    transform: scale(1.05);
    border-color: rgba(245, 158, 11, 1);
    box-shadow:
        0 25px 60px rgba(0, 0, 0, 0.4),
        0 0 0 6px rgba(255, 255, 255, 0.3),
        inset 0 0 0 3px rgba(245, 158, 11, 0.5);
}

.hero-profile:hover img {
    transform: scale(1.08);
}

/* Profile Placeholder */
.hero-profile-placeholder {
    width: 280px;
    height: 280px;
    border-radius: 50%;
    margin: 0 auto 2rem;
    border: 6px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, var(--secondary-color), #10b981);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 120px;
    color: white;
    transition: all 0.4s ease;
    cursor: pointer;
    position: relative;
}

.hero-profile-placeholder:hover {
    transform: scale(1.08) rotate(5deg);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
}

.hero-profile-placeholder::after {
    content: 'اضغط لإضافة الصورة';
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
}

.hero-profile-placeholder:hover::after {
    opacity: 1;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    right: 50%;
    transform: translateX(50%);
    z-index: 3;
}

.scroll-arrow {
    color: var(--white);
    font-size: var(--font-size-2xl);
    animation: bounce 2s infinite;
    cursor: pointer;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== Official Portrait Section ===== */
.official-portrait {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(30, 58, 138, 0.1);
}

.portrait-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.portrait-text h2 {
    color: var(--primary-color);
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
    font-weight: 700;
}

.portrait-title {
    color: var(--accent-color);
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
}

.portrait-description {
    color: var(--dark-gray);
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-xl);
}

.portrait-credentials {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.credential-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--dark-gray);
    font-weight: 500;
}

.credential-item i {
    color: var(--secondary-color);
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.portrait-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.portrait-frame {
    position: relative;
    width: 400px;
    height: 500px;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow:
        var(--shadow-xl),
        0 0 0 4px rgba(245, 158, 11, 0.3),
        0 0 0 8px rgba(255, 255, 255, 0.5);
    transition: all 0.4s ease;
}

.portrait-frame:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.2),
        0 0 0 6px rgba(245, 158, 11, 0.5),
        0 0 0 12px rgba(255, 255, 255, 0.7);
}

.portrait-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top;
    transition: transform 0.4s ease;
    filter: brightness(1.05) contrast(1.1);
}

.portrait-frame:hover .portrait-img {
    transform: scale(1.05);
}

.portrait-badge {
    position: absolute;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    background: var(--primary-gradient);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-md);
    z-index: 10;
}

.portrait-badge i {
    color: var(--accent-color);
    animation: sparkle 2s ease-in-out infinite;
}

/* ===== Quick Stats ===== */
.quick-stats {
    background: var(--light-gray);
    padding: var(--spacing-3xl) 0;
    margin-top: -1px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.stat-item {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.stat-icon i {
    font-size: var(--font-size-3xl);
    color: var(--white);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-lg);
    color: var(--gray);
    margin: 0;
}

/* ===== About Section ===== */
#about {
    background: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.about-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-xl);
}

.about-img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow:
        var(--shadow-lg),
        0 0 0 3px rgba(245, 158, 11, 0.3),
        0 0 0 6px rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
    filter: brightness(1.02) contrast(1.05);
}

.about-img:hover {
    transform: scale(1.02);
    box-shadow:
        var(--shadow-xl),
        0 0 0 4px rgba(245, 158, 11, 0.5),
        0 0 0 8px rgba(255, 255, 255, 0.2);
}

.about-quote {
    position: absolute;
    bottom: -30px;
    right: -30px;
    background: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 300px;
    border-right: 4px solid var(--secondary-color);
}

.about-quote i {
    font-size: var(--font-size-2xl);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.about-quote p {
    font-style: italic;
    color: var(--dark-gray);
    margin: 0;
    font-size: var(--font-size-sm);
}

.about-text h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.about-text p {
    margin-bottom: var(--spacing-xl);
    line-height: 1.8;
}

.vision-points {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.vision-point {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.vision-point:hover {
    background: rgba(30, 58, 138, 0.1);
}

.vision-point i {
    color: var(--secondary-color);
    font-size: var(--font-size-lg);
}

.vision-point span {
    font-weight: 600;
    color: var(--dark-gray);
}

/* ===== Program Section ===== */
#program {
    background: var(--light-gray);
}

.program-content {
    max-width: 1000px;
    margin: 0 auto;
}

.program-tabs {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-3xl);
    flex-wrap: wrap;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--white);
    color: var(--gray);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 600;
}

.tab-btn:hover,
.tab-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.tab-btn i {
    font-size: var(--font-size-lg);
}

.tab-panel {
    display: none;
    background: var(--white);
    padding: var(--spacing-3xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.tab-panel.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-panel h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    font-size: var(--font-size-2xl);
}

.program-items {
    display: grid;
    gap: var(--spacing-lg);
}

.program-item {
    display: flex;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--light-gray);
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
    border-right: 4px solid var(--secondary-color);
}

.program-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow-md);
}

.program-item i {
    font-size: var(--font-size-2xl);
    color: var(--secondary-color);
    margin-top: var(--spacing-xs);
    flex-shrink: 0;
}

.program-item h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.program-item p {
    color: var(--gray);
    margin: 0;
    line-height: 1.6;
}

/* ===== Achievements Section ===== */
#achievements {
    background: var(--white);
}

.achievements-filter {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-3xl);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--light-gray);
    color: var(--gray);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 600;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--secondary-color);
    color: var(--white);
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.achievement-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
    border-top: 4px solid var(--primary-color);
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.achievement-card[data-category="business"] {
    border-top-color: var(--primary-color);
}

.achievement-card[data-category="charity"] {
    border-top-color: var(--secondary-color);
}

.achievement-card[data-category="social"] {
    border-top-color: var(--accent-color);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    background: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: var(--spacing-xl) auto var(--spacing-lg);
}

.achievement-icon i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.achievement-content {
    padding: 0 var(--spacing-xl) var(--spacing-xl);
    text-align: center;
}

.achievement-content h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.achievement-type {
    color: var(--secondary-color);
    font-weight: 600;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.achievement-description {
    color: var(--gray);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.achievement-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.achievement-stats span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--dark-gray);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.achievement-stats i {
    color: var(--accent-color);
}

/* ===== Gallery Section ===== */
#gallery {
    background: var(--light-gray);
}

.gallery-filter {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-3xl);
    flex-wrap: wrap;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    cursor: pointer;
}

.gallery-item:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-xl);
}

.gallery-img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 58, 138, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Featured Gallery Item (للصورة الرسمية) */
.gallery-item.featured {
    grid-column: span 2;
    position: relative;
    background: var(--primary-gradient);
    border: 3px solid var(--accent-color);
}

.gallery-item.featured .gallery-img {
    height: 350px;
    filter: brightness(1.1) contrast(1.05);
}

.gallery-item.featured .gallery-overlay {
    background: rgba(30, 58, 138, 0.7);
}

.featured-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--accent-color);
    color: var(--white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-md);
    z-index: 10;
}

.featured-badge i {
    color: var(--white);
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

.gallery-content {
    text-align: center;
    color: var(--white);
    padding: var(--spacing-lg);
}

.gallery-content h3 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.gallery-content p {
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
}

.gallery-btn {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: 2px solid var(--white);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.gallery-btn:hover {
    background: var(--white);
    color: var(--primary-color);
}

.video-indicator {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    color: var(--white);
    font-size: var(--font-size-2xl);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* ===== Forms ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--dark-gray);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid #e2e8f0;
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: var(--transition-fast);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    margin-bottom: var(--spacing-sm);
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.volunteer-types {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
}

/* ===== Join Section ===== */
#join {
    background: var(--white);
}

.join-content {
    max-width: 1000px;
    margin: 0 auto;
}

.join-intro {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    margin-bottom: var(--spacing-3xl);
}

.join-text h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.join-benefits {
    margin-top: var(--spacing-xl);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.benefit-item:hover {
    background: rgba(5, 150, 105, 0.1);
}

.benefit-item i {
    color: var(--secondary-color);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.volunteers-img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.volunteer-form-container {
    background: var(--light-gray);
    padding: var(--spacing-3xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.volunteer-form h3 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-2xl);
}

.btn-submit {
    width: 100%;
    padding: var(--spacing-lg);
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-lg);
}

/* ===== Contact Section ===== */
#contact {
    background: var(--light-gray);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-3xl);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-item {
    display: flex;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: var(--white);
    font-size: var(--font-size-xl);
}

.contact-details h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.contact-details p {
    color: var(--gray);
    margin: 0;
    line-height: 1.6;
}

.contact-details a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.contact-details a:hover {
    color: var(--secondary-color);
}

.contact-form-container {
    background: var(--white);
    padding: var(--spacing-3xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.contact-form h3 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-2xl);
}

.social-media {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.social-media h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xl);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: var(--transition-normal);
    font-weight: 600;
}

.social-link.facebook {
    background: #1877f2;
    color: var(--white);
}

.social-link.telegram {
    background: #0088cc;
    color: var(--white);
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    color: var(--white);
}

.social-link.youtube {
    background: #ff0000;
    color: var(--white);
}

.social-link.twitter {
    background: #1da1f2;
    color: var(--white);
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.map-container {
    text-align: center;
}

.map-container h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-xl);
}

.map-placeholder {
    background: var(--white);
    padding: var(--spacing-3xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    color: var(--gray);
}

.map-placeholder i {
    font-size: var(--font-size-5xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

/* ===== Footer ===== */
.footer {
    background: var(--black);
    color: var(--white);
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.footer-logo {
    text-align: center;
}

.footer-logo-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: var(--spacing-md);
}

.footer-logo h3 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.footer-logo p {
    color: var(--gray);
    font-style: italic;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--gray);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.footer-contact {
    list-style: none;
}

.footer-contact li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    color: var(--gray);
}

.footer-contact i {
    color: var(--secondary-color);
    width: 20px;
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.social-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-icon:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--dark-gray);
    padding-top: var(--spacing-xl);
    text-align: center;
    color: var(--gray);
}

.footer-bottom p {
    margin-bottom: var(--spacing-sm);
}

.footer-bottom i {
    color: var(--secondary-color);
}

/* ===== Modals ===== */
.lightbox,
.video-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: var(--z-modal);
    justify-content: center;
    align-items: center;
}

.lightbox.active,
.video-modal.active {
    display: flex;
}

.lightbox-content,
.video-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-close,
.video-modal-close {
    position: absolute;
    top: -40px;
    left: 0;
    color: var(--white);
    font-size: var(--font-size-3xl);
    cursor: pointer;
    transition: var(--transition-fast);
}

.lightbox-close:hover,
.video-modal-close:hover {
    color: var(--secondary-color);
}

#lightbox-img {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--radius-lg);
}

#modal-video {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--radius-lg);
}

/* ===== Back to Top ===== */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    left: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: var(--z-fixed);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.back-to-top:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.back-to-top.show {
    display: flex;
}

/* ===== Success Message ===== */
.success-message {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    background: var(--secondary-color);
    color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    z-index: var(--z-modal);
    animation: slideIn 0.3s ease-out;
}

.success-message.show {
    display: flex;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== Utility Classes ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}