<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صورة تجريبية - الأستاذ رحيم مجيسر</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .placeholder-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 100%;
        }
        
        .profile-placeholder {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(45deg, #059669, #10b981);
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 120px;
            color: white;
            border: 5px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .slogan {
            font-size: 1.5rem;
            margin-bottom: 30px;
            opacity: 0.9;
            font-style: italic;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: right;
        }
        
        .instructions h3 {
            color: #fbbf24;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            text-align: right;
            padding-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .download-btn {
            background: #059669;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .download-btn:hover {
            background: #047857;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        @media (max-width: 768px) {
            .profile-placeholder {
                width: 200px;
                height: 200px;
                font-size: 80px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .slogan {
                font-size: 1.2rem;
            }
            
            .placeholder-container {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="placeholder-container">
        <div class="profile-placeholder">
            👤
        </div>
        
        <h1>الأستاذ فراس رحيم مجيسر</h1>
        <div class="slogan">صوتٌ للعطاء، وعدٌ للتغيير</div>
        
        <div class="instructions">
            <h3>📸 لإضافة الصورة الشخصية:</h3>
            <ol>
                <li>احصل على صورة شخصية واضحة ومهنية</li>
                <li>تأكد من أن الصورة بجودة عالية (800x600 بكسل على الأقل)</li>
                <li>احفظ الصورة باسم <strong>firas-hero.jpg</strong></li>
                <li>ضع الصورة في مجلد <strong>images</strong></li>
                <li>أعد تحميل الموقع لرؤية الصورة</li>
            </ol>
        </div>
        
        <a href="../index.html" class="download-btn">
            🏠 العودة للموقع الرئيسي
        </a>
    </div>
    
    <script>
        // إضافة تأثير تفاعلي للصورة التجريبية
        const placeholder = document.querySelector('.profile-placeholder');
        
        placeholder.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05) rotate(5deg)';
            this.style.transition = 'all 0.3s ease';
        });
        
        placeholder.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
        
        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎯 مرحباً بك في موقع الأستاذ رحيم مجيسر!');
            console.log('📸 لا تنس إضافة الصورة الشخصية في مجلد images');
        }, 1000);
    </script>
</body>
</html>