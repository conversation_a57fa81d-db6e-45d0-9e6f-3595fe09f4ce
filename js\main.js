// ===== Main JavaScript File =====

// Global Variables
let currentSection = 'home';
let isLoading = true;
let scrollPosition = 0;

// DOM Elements
const loadingScreen = document.getElementById('loading-screen');
const navbar = document.getElementById('navbar');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const sections = document.querySelectorAll('.section');
const backToTopBtn = document.getElementById('backToTop');
const successMessage = document.getElementById('successMessage');

// ===== Initialization =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Hide loading screen after 2 seconds
    setTimeout(() => {
        hideLoadingScreen();
    }, 2000);
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Initialize animations
    initializeAnimations();
    
    // Initialize counters
    initializeCounters();
    
    // Initialize tabs
    initializeTabs();
    
    // Initialize filters
    initializeFilters();
    
    // Show home section by default
    showSection('home');
    
    console.log('🎉 موقع الأستاذ رحيم مجيسر تم تحميله بنجاح!');
}

// ===== Loading Screen =====
function hideLoadingScreen() {
    loadingScreen.style.opacity = '0';
    setTimeout(() => {
        loadingScreen.style.display = 'none';
        isLoading = false;
        document.body.style.overflow = 'auto';
    }, 500);
}

// ===== Event Listeners =====
function initializeEventListeners() {
    // Navigation
    hamburger.addEventListener('click', toggleMobileMenu);
    
    // Navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavClick);
    });
    
    // Scroll events
    window.addEventListener('scroll', handleScroll);
    
    // Back to top button
    backToTopBtn.addEventListener('click', scrollToTop);
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navbar.contains(e.target) && navMenu.classList.contains('active')) {
            closeMobileMenu();
        }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Window resize
    window.addEventListener('resize', handleResize);
    
    // Prevent right-click on images (optional)
    document.querySelectorAll('img').forEach(img => {
        img.addEventListener('contextmenu', e => e.preventDefault());
    });
}

// ===== Navigation Functions =====
function toggleMobileMenu() {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
    document.body.style.overflow = navMenu.classList.contains('active') ? 'hidden' : 'auto';
}

function closeMobileMenu() {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function handleNavClick(e) {
    e.preventDefault();
    const sectionId = e.currentTarget.getAttribute('data-section');
    showSection(sectionId);
    closeMobileMenu();
}

function showSection(sectionId) {
    // Hide all sections
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionId;
        
        // Update active nav link
        updateActiveNavLink(sectionId);
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // Update page title
        updatePageTitle(sectionId);
        
        // Trigger section-specific animations
        triggerSectionAnimations(sectionId);
    }
}

function updateActiveNavLink(sectionId) {
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-section') === sectionId) {
            link.classList.add('active');
        }
    });
}

function updatePageTitle(sectionId) {
    const titles = {
        'home': 'الأستاذ رحيم مجيسر - صوتٌ للعطاء، وعدٌ للتغيير',
        'about': 'من هو رحيم مجيسر؟ - الأستاذ رحيم مجيسر',
        'program': 'البرنامج الانتخابي - الأستاذ رحيم مجيسر',
        'achievements': 'الإنجازات - الأستاذ رحيم مجيسر',
        'gallery': 'معرض الصور - الأستاذ رحيم مجيسر',
        'join': 'انضم إلى الحملة - الأستاذ رحيم مجيسر',
        'contact': 'اتصل بنا - الأستاذ رحيم مجيسر'
    };
    
    document.title = titles[sectionId] || titles['home'];
}

// ===== Scroll Functions =====
function handleScroll() {
    scrollPosition = window.pageYOffset;
    
    // Update navbar appearance
    if (scrollPosition > 100) {
        navbar.classList.add('scrolled');
        backToTopBtn.classList.add('show');
    } else {
        navbar.classList.remove('scrolled');
        backToTopBtn.classList.remove('show');
    }
    
    // Parallax effects (if needed)
    handleParallaxEffects();
}

function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

function handleParallaxEffects() {
    // Add parallax effects here if needed
    const heroSection = document.querySelector('.hero');
    if (heroSection && currentSection === 'home') {
        const scrolled = window.pageYOffset;
        const parallax = scrolled * 0.5;
        heroSection.style.transform = `translateY(${parallax}px)`;
    }
}

// ===== Animation Functions =====
function initializeAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.stat-item, .achievement-card, .gallery-item, .program-item').forEach(el => {
        observer.observe(el);
    });
}

function triggerSectionAnimations(sectionId) {
    const section = document.getElementById(sectionId);
    if (!section) return;
    
    // Add slide-up animation to section content
    const animatedElements = section.querySelectorAll('.section-header, .about-content, .program-content, .achievements-content, .gallery-content, .join-content, .contact-content');
    
    animatedElements.forEach((el, index) => {
        setTimeout(() => {
            el.classList.add('slide-up');
        }, index * 100);
    });
}

// ===== Counter Animation =====
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-count'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    };
    
    // Intersection Observer for counters
    const counterObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// ===== Tab System =====
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));
            
            // Add active class to clicked button and corresponding panel
            this.classList.add('active');
            const targetPanel = document.getElementById(targetTab);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }
        });
    });
}

// ===== Filter System =====
function initializeFilters() {
    // Achievements filter
    const achievementFilters = document.querySelectorAll('#achievements .filter-btn');
    const achievementCards = document.querySelectorAll('.achievement-card');
    
    achievementFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            const filterValue = this.getAttribute('data-filter');
            
            // Update active filter button
            achievementFilters.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter cards
            achievementCards.forEach(card => {
                if (filterValue === 'all' || card.getAttribute('data-category') === filterValue) {
                    card.style.display = 'block';
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'scale(1)';
                    }, 100);
                } else {
                    card.style.opacity = '0';
                    card.style.transform = 'scale(0.8)';
                    setTimeout(() => {
                        card.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
    
    // Gallery filter
    const galleryFilters = document.querySelectorAll('#gallery .filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    galleryFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            const filterValue = this.getAttribute('data-filter');
            
            // Update active filter button
            galleryFilters.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter items
            galleryItems.forEach(item => {
                if (filterValue === 'all' || item.getAttribute('data-category') === filterValue) {
                    item.style.display = 'block';
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'scale(1)';
                    }, 100);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'scale(0.8)';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// ===== Lightbox Functions =====
function openLightbox(imageSrc) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightbox-img');
    
    lightboxImg.src = imageSrc;
    lightbox.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.remove('active');
    document.body.style.overflow = 'auto';
}

// ===== Video Modal Functions =====
function openVideoModal(videoSrc) {
    const videoModal = document.getElementById('videoModal');
    const modalVideo = document.getElementById('modal-video');
    
    modalVideo.src = videoSrc;
    videoModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeVideoModal() {
    const videoModal = document.getElementById('videoModal');
    const modalVideo = document.getElementById('modal-video');
    
    modalVideo.pause();
    modalVideo.src = '';
    videoModal.classList.remove('active');
    document.body.style.overflow = 'auto';
}

// ===== Utility Functions =====
function showSuccessMessage(message = 'تم إرسال رسالتك بنجاح!') {
    const messageEl = successMessage.querySelector('span');
    messageEl.textContent = message;
    successMessage.classList.add('show');
    
    setTimeout(() => {
        successMessage.classList.remove('show');
    }, 3000);
}

function handleKeyboardNavigation(e) {
    // ESC key to close modals
    if (e.key === 'Escape') {
        closeLightbox();
        closeVideoModal();
        closeMobileMenu();
    }
    
    // Arrow keys for section navigation
    if (e.altKey) {
        const sectionOrder = ['home', 'about', 'program', 'achievements', 'gallery', 'join', 'contact'];
        const currentIndex = sectionOrder.indexOf(currentSection);
        
        if (e.key === 'ArrowRight' && currentIndex > 0) {
            showSection(sectionOrder[currentIndex - 1]);
        } else if (e.key === 'ArrowLeft' && currentIndex < sectionOrder.length - 1) {
            showSection(sectionOrder[currentIndex + 1]);
        }
    }
}

function handleResize() {
    // Close mobile menu on resize to desktop
    if (window.innerWidth > 991) {
        closeMobileMenu();
    }
    
    // Recalculate animations if needed
    if (typeof recalculateAnimations === 'function') {
        recalculateAnimations();
    }
}

// ===== Smooth Scrolling for Anchor Links =====
function smoothScrollTo(target, duration = 1000) {
    const targetElement = document.querySelector(target);
    if (!targetElement) return;
    
    const targetPosition = targetElement.offsetTop - 80; // Account for fixed navbar
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;
    
    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }
    
    function ease(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }
    
    requestAnimationFrame(animation);
}

// ===== Local Storage Functions =====
function saveToLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (e) {
        console.warn('Could not save to localStorage:', e);
    }
}

function getFromLocalStorage(key) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
    } catch (e) {
        console.warn('Could not read from localStorage:', e);
        return null;
    }
}

// ===== Performance Optimization =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Apply throttling to scroll handler
window.addEventListener('scroll', throttle(handleScroll, 16)); // ~60fps

// ===== Error Handling =====
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // You can add error reporting here
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    // You can add error reporting here
});

// ===== Accessibility Enhancements =====
function enhanceAccessibility() {
    // Add ARIA labels to interactive elements
    const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
    interactiveElements.forEach(el => {
        if (!el.getAttribute('aria-label') && !el.getAttribute('aria-labelledby')) {
            const text = el.textContent || el.value || el.placeholder;
            if (text) {
                el.setAttribute('aria-label', text.trim());
            }
        }
    });
    
    // Add focus indicators
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

// Initialize accessibility enhancements
document.addEventListener('DOMContentLoaded', enhanceAccessibility);

// ===== Image Management Functions =====
function showImageInstructions() {
    const message = `📸 لإضافة صورتك الشخصية:

1. احصل على صورة واضحة ومهنية
2. احفظ الصورة باسم "firas-hero.jpg"
3. ضع الصورة في مجلد "images"
4. أعد تحميل الصفحة

أو يمكنك فتح ملف image-placeholder.html للمزيد من التفاصيل.`;
    
    if (confirm(message + '\n\nهل تريد فتح صفحة التعليمات؟')) {
        window.open('images/image-placeholder.html', '_blank');
    }
}

function checkHeroImage() {
    const heroProfile = document.getElementById('hero-profile');
    if (!heroProfile) return;
    
    const img = heroProfile.querySelector('img');
    
    if (img && img.complete && img.naturalHeight !== 0) {
        // الصورة موجودة ومحملة بنجاح
        heroProfile.classList.add('image-loaded');
        console.log('✅ تم تحميل الصورة الشخصية بنجاح');
    } else {
        // الصورة غير موجودة، عرض placeholder
        heroProfile.innerHTML = `
            <div class="hero-profile-placeholder" onclick="showImageInstructions()">
                <i class="fas fa-user"></i>
            </div>
        `;
        console.log('⚠️ الصورة الشخصية غير موجودة، يتم عرض placeholder');
    }
}

// تحقق من الصورة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(checkHeroImage, 1000);
});

// ===== Export functions for use in other files =====
window.siteUtils = {
    showSection,
    showSuccessMessage,
    openLightbox,
    closeLightbox,
    openVideoModal,
    closeVideoModal,
    saveToLocalStorage,
    getFromLocalStorage,
    smoothScrollTo
};

// ===== Console Welcome Message =====
console.log(`
🌟 مرحباً بك في موقع الأستاذ فراس رحيم مجيسر الانتخابي
📧 للتواصل: <EMAIL>
🔧 تم التطوير بـ ❤️ لخدمة بغداد والعراق
`);

// ===== Service Worker Registration (for PWA support) =====
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}