// ===== Forms JavaScript File =====

// Form Configuration
const formConfig = {
    validation: {
        required: 'هذا الحقل مطلوب',
        email: 'يرجى إدخال بريد إلكتروني صحيح',
        phone: 'يرجى إدخال رقم هاتف صحيح',
        minLength: 'يجب أن يحتوي على {min} أحرف على الأقل',
        maxLength: 'يجب ألا يزيد عن {max} حرف',
        pattern: 'تنسيق غير صحيح'
    },
    messages: {
        success: 'تم إرسال رسالتك بنجاح!',
        error: 'حدث خطأ، يرجى المحاولة مرة أخرى',
        loading: 'جاري الإرسال...'
    }
};

// ===== Form Validator Class =====
class FormValidator {
    constructor(form) {
        this.form = form;
        this.errors = {};
        this.rules = {};
        
        this.init();
    }
    
    init() {
        this.setupValidationRules();
        this.attachEventListeners();
    }
    
    setupValidationRules() {
        const inputs = this.form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            const rules = [];
            
            // Required validation
            if (input.hasAttribute('required')) {
                rules.push({ type: 'required' });
            }
            
            // Email validation
            if (input.type === 'email') {
                rules.push({ type: 'email' });
            }
            
            // Phone validation
            if (input.type === 'tel') {
                rules.push({ type: 'phone' });
            }
            
            // Min length validation
            if (input.hasAttribute('minlength')) {
                rules.push({ 
                    type: 'minLength', 
                    value: parseInt(input.getAttribute('minlength')) 
                });
            }
            
            // Max length validation
            if (input.hasAttribute('maxlength')) {
                rules.push({ 
                    type: 'maxLength', 
                    value: parseInt(input.getAttribute('maxlength')) 
                });
            }
            
            // Pattern validation
            if (input.hasAttribute('pattern')) {
                rules.push({ 
                    type: 'pattern', 
                    value: new RegExp(input.getAttribute('pattern')) 
                });
            }
            
            if (rules.length > 0) {
                this.rules[input.name] = rules;
            }
        });
    }
    
    attachEventListeners() {
        // Real-time validation
        const inputs = this.form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
        
        // Form submission
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
    }
    
    validateField(input) {
        const fieldName = input.name;
        const fieldValue = input.value.trim();
        const rules = this.rules[fieldName];
        
        if (!rules) return true;
        
        // Clear previous errors
        delete this.errors[fieldName];
        
        for (const rule of rules) {
            const isValid = this.applyRule(fieldValue, rule);
            if (!isValid) {
                this.errors[fieldName] = this.getErrorMessage(rule);
                this.showFieldError(input, this.errors[fieldName]);
                return false;
            }
        }
        
        this.clearFieldError(input);
        return true;
    }
    
    applyRule(value, rule) {
        switch (rule.type) {
            case 'required':
                return value.length > 0;
            
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return !value || emailRegex.test(value);
            
            case 'phone':
                const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
                return !value || phoneRegex.test(value);
            
            case 'minLength':
                return !value || value.length >= rule.value;
            
            case 'maxLength':
                return !value || value.length <= rule.value;
            
            case 'pattern':
                return !value || rule.value.test(value);
            
            default:
                return true;
        }
    }
    
    getErrorMessage(rule) {
        let message = formConfig.validation[rule.type];
        
        if (rule.value && message.includes('{')) {
            message = message.replace('{min}', rule.value).replace('{max}', rule.value);
        }
        
        return message;
    }
    
    showFieldError(input, message) {
        this.clearFieldError(input);
        
        input.classList.add('error');
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        
        input.parentNode.appendChild(errorElement);
    }
    
    clearFieldError(input) {
        input.classList.remove('error');
        
        const existingError = input.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }
    
    validateForm() {
        this.errors = {};
        const inputs = this.form.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    handleSubmit(e) {
        e.preventDefault();
        
        if (this.validateForm()) {
            this.submitForm();
        } else {
            this.focusFirstError();
        }
    }
    
    focusFirstError() {
        const firstErrorField = this.form.querySelector('.error');
        if (firstErrorField) {
            firstErrorField.focus();
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
    
    async submitForm() {
        const formData = new FormData(this.form);
        const submitButton = this.form.querySelector('button[type="submit"]');
        
        // Show loading state
        this.setLoadingState(submitButton, true);
        
        try {
            // Simulate API call (replace with actual endpoint)
            await this.sendFormData(formData);
            
            // Show success message
            this.showSuccessMessage();
            
            // Reset form
            this.form.reset();
            
        } catch (error) {
            console.error('Form submission error:', error);
            this.showErrorMessage();
        } finally {
            this.setLoadingState(submitButton, false);
        }
    }
    
    async sendFormData(formData) {
        // Convert FormData to object
        const data = {};
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                // Handle multiple values (like checkboxes)
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        // Add timestamp
        data.timestamp = new Date().toISOString();
        data.formType = this.form.id;
        
        // Save to localStorage (for demo purposes)
        this.saveToLocalStorage(data);
        
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Here you would normally send to your server
        // const response = await fetch('/api/submit-form', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(data)
        // });
        // 
        // if (!response.ok) {
        //     throw new Error('Network response was not ok');
        // }
        
        return { success: true };
    }
    
    saveToLocalStorage(data) {
        const existingData = JSON.parse(localStorage.getItem('formSubmissions') || '[]');
        existingData.push(data);
        localStorage.setItem('formSubmissions', JSON.stringify(existingData));
    }
    
    setLoadingState(button, isLoading) {
        if (isLoading) {
            button.disabled = true;
            button.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                ${formConfig.messages.loading}
            `;
        } else {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text') || 'إرسال';
            button.innerHTML = `
                <i class="fas fa-paper-plane"></i>
                ${originalText}
            `;
        }
    }
    
    showSuccessMessage() {
        if (window.siteUtils && window.siteUtils.showSuccessMessage) {
            window.siteUtils.showSuccessMessage(formConfig.messages.success);
        } else {
            alert(formConfig.messages.success);
        }
    }
    
    showErrorMessage() {
        alert(formConfig.messages.error);
    }
}

// ===== Form Enhancement Class =====
class FormEnhancer {
    constructor(form) {
        this.form = form;
        this.init();
    }
    
    init() {
        this.enhanceInputs();
        this.addFloatingLabels();
        this.addCharacterCounters();
        this.addPasswordToggle();
        this.addFileUploadEnhancement();
    }
    
    enhanceInputs() {
        const inputs = this.form.querySelectorAll('input, textarea');
        
        inputs.forEach(input => {
            // Add focus/blur animations
            input.addEventListener('focus', () => {
                input.parentNode.classList.add('focused');
            });
            
            input.addEventListener('blur', () => {
                if (!input.value) {
                    input.parentNode.classList.remove('focused');
                }
            });
            
            // Auto-resize textareas
            if (input.tagName === 'TEXTAREA') {
                this.autoResizeTextarea(input);
            }
            
            // Format phone numbers
            if (input.type === 'tel') {
                this.formatPhoneNumber(input);
            }
        });
    }
    
    addFloatingLabels() {
        const formGroups = this.form.querySelectorAll('.form-group');
        
        formGroups.forEach(group => {
            const input = group.querySelector('input, textarea, select');
            const label = group.querySelector('label');
            
            if (input && label && !group.classList.contains('checkbox-label')) {
                group.classList.add('floating-label');
                
                // Check if input has value on load
                if (input.value) {
                    group.classList.add('focused');
                }
            }
        });
    }
    
    addCharacterCounters() {
        const textareas = this.form.querySelectorAll('textarea[maxlength]');
        
        textareas.forEach(textarea => {
            const maxLength = parseInt(textarea.getAttribute('maxlength'));
            const counter = document.createElement('div');
            counter.className = 'character-counter';
            counter.textContent = `0 / ${maxLength}`;
            
            textarea.parentNode.appendChild(counter);
            
            textarea.addEventListener('input', () => {
                const currentLength = textarea.value.length;
                counter.textContent = `${currentLength} / ${maxLength}`;
                
                if (currentLength > maxLength * 0.9) {
                    counter.classList.add('warning');
                } else {
                    counter.classList.remove('warning');
                }
            });
        });
    }
    
    addPasswordToggle() {
        const passwordInputs = this.form.querySelectorAll('input[type="password"]');
        
        passwordInputs.forEach(input => {
            const wrapper = document.createElement('div');
            wrapper.className = 'password-wrapper';
            
            input.parentNode.insertBefore(wrapper, input);
            wrapper.appendChild(input);
            
            const toggleButton = document.createElement('button');
            toggleButton.type = 'button';
            toggleButton.className = 'password-toggle';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
            
            wrapper.appendChild(toggleButton);
            
            toggleButton.addEventListener('click', () => {
                const isPassword = input.type === 'password';
                input.type = isPassword ? 'text' : 'password';
                toggleButton.innerHTML = isPassword ? 
                    '<i class="fas fa-eye-slash"></i>' : 
                    '<i class="fas fa-eye"></i>';
            });
        });
    }
    
    addFileUploadEnhancement() {
        const fileInputs = this.form.querySelectorAll('input[type="file"]');
        
        fileInputs.forEach(input => {
            const wrapper = document.createElement('div');
            wrapper.className = 'file-upload-wrapper';
            
            const label = document.createElement('label');
            label.className = 'file-upload-label';
            label.setAttribute('for', input.id);
            label.innerHTML = `
                <i class="fas fa-cloud-upload-alt"></i>
                <span>اختر ملف أو اسحبه هنا</span>
            `;
            
            input.parentNode.insertBefore(wrapper, input);
            wrapper.appendChild(input);
            wrapper.appendChild(label);
            
            // Drag and drop functionality
            wrapper.addEventListener('dragover', (e) => {
                e.preventDefault();
                wrapper.classList.add('dragover');
            });
            
            wrapper.addEventListener('dragleave', () => {
                wrapper.classList.remove('dragover');
            });
            
            wrapper.addEventListener('drop', (e) => {
                e.preventDefault();
                wrapper.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    input.files = files;
                    this.updateFileLabel(input, label);
                }
            });
            
            input.addEventListener('change', () => {
                this.updateFileLabel(input, label);
            });
        });
    }
    
    updateFileLabel(input, label) {
        const files = input.files;
        if (files.length > 0) {
            const fileName = files.length === 1 ? files[0].name : `${files.length} ملفات محددة`;
            label.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <span>${fileName}</span>
            `;
            label.classList.add('has-file');
        } else {
            label.innerHTML = `
                <i class="fas fa-cloud-upload-alt"></i>
                <span>اختر ملف أو اسحبه هنا</span>
            `;
            label.classList.remove('has-file');
        }
    }
    
    autoResizeTextarea(textarea) {
        const resize = () => {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        };
        
        textarea.addEventListener('input', resize);
        resize(); // Initial resize
    }
    
    formatPhoneNumber(input) {
        input.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            
            // Iraqi phone number format
            if (value.startsWith('964')) {
                value = value.replace(/^964/, '+964 ');
                if (value.length > 8) {
                    value = value.replace(/(\+964 )(\d{3})(\d{3})(\d{4})/, '$1$2 $3 $4');
                }
            } else if (value.startsWith('07')) {
                value = value.replace(/^07/, '07');
                if (value.length > 3) {
                    value = value.replace(/^(\d{4})(\d{3})(\d{4})/, '$1 $2 $3');
                }
            }
            
            e.target.value = value;
        });
    }
}

// ===== Multi-Step Form Class =====
class MultiStepForm {
    constructor(form) {
        this.form = form;
        this.steps = form.querySelectorAll('.form-step');
        this.currentStep = 0;
        this.validator = new FormValidator(form);
        
        this.init();
    }
    
    init() {
        this.createProgressBar();
        this.createNavigationButtons();
        this.showStep(0);
    }
    
    createProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.className = 'form-progress';
        
        const progressFill = document.createElement('div');
        progressFill.className = 'progress-fill';
        progressBar.appendChild(progressFill);
        
        const progressSteps = document.createElement('div');
        progressSteps.className = 'progress-steps';
        
        this.steps.forEach((step, index) => {
            const stepIndicator = document.createElement('div');
            stepIndicator.className = 'progress-step';
            stepIndicator.textContent = index + 1;
            progressSteps.appendChild(stepIndicator);
        });
        
        progressBar.appendChild(progressSteps);
        this.form.insertBefore(progressBar, this.form.firstChild);
        
        this.progressBar = progressBar;
    }
    
    createNavigationButtons() {
        const navigation = document.createElement('div');
        navigation.className = 'form-navigation';
        
        const prevButton = document.createElement('button');
        prevButton.type = 'button';
        prevButton.className = 'btn btn-secondary prev-btn';
        prevButton.innerHTML = '<i class="fas fa-arrow-right"></i> السابق';
        prevButton.addEventListener('click', () => this.prevStep());
        
        const nextButton = document.createElement('button');
        nextButton.type = 'button';
        nextButton.className = 'btn btn-primary next-btn';
        nextButton.innerHTML = 'التالي <i class="fas fa-arrow-left"></i>';
        nextButton.addEventListener('click', () => this.nextStep());
        
        navigation.appendChild(prevButton);
        navigation.appendChild(nextButton);
        
        this.form.appendChild(navigation);
        
        this.prevButton = prevButton;
        this.nextButton = nextButton;
    }
    
    showStep(stepIndex) {
        // Hide all steps
        this.steps.forEach(step => step.classList.remove('active'));
        
        // Show current step
        this.steps[stepIndex].classList.add('active');
        
        // Update progress
        this.updateProgress(stepIndex);
        
        // Update navigation buttons
        this.updateNavigation(stepIndex);
        
        this.currentStep = stepIndex;
    }
    
    updateProgress(stepIndex) {
        const progressFill = this.progressBar.querySelector('.progress-fill');
        const progressSteps = this.progressBar.querySelectorAll('.progress-step');
        
        const progressPercentage = (stepIndex / (this.steps.length - 1)) * 100;
        progressFill.style.width = `${progressPercentage}%`;
        
        progressSteps.forEach((step, index) => {
            if (index <= stepIndex) {
                step.classList.add('completed');
            } else {
                step.classList.remove('completed');
            }
        });
    }
    
    updateNavigation(stepIndex) {
        this.prevButton.style.display = stepIndex === 0 ? 'none' : 'inline-flex';
        
        if (stepIndex === this.steps.length - 1) {
            this.nextButton.innerHTML = 'إرسال <i class="fas fa-paper-plane"></i>';
            this.nextButton.type = 'submit';
        } else {
            this.nextButton.innerHTML = 'التالي <i class="fas fa-arrow-left"></i>';
            this.nextButton.type = 'button';
        }
    }
    
    nextStep() {
        if (this.validateCurrentStep()) {
            if (this.currentStep < this.steps.length - 1) {
                this.showStep(this.currentStep + 1);
            }
        }
    }
    
    prevStep() {
        if (this.currentStep > 0) {
            this.showStep(this.currentStep - 1);
        }
    }
    
    validateCurrentStep() {
        const currentStepElement = this.steps[this.currentStep];
        const inputs = currentStepElement.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validator.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
}

// ===== Initialize Forms =====
document.addEventListener('DOMContentLoaded', function() {
    // Add form styles
    addFormStyles();
    
    // Initialize all forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        // Check if it's a multi-step form
        if (form.querySelectorAll('.form-step').length > 1) {
            new MultiStepForm(form);
        } else {
            new FormValidator(form);
        }
        
        // Enhance form
        new FormEnhancer(form);
        
        // Store original button text
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.setAttribute('data-original-text', submitButton.textContent.trim());
        }
    });
});

// ===== Add Form Styles =====
function addFormStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Form Error Styles */
        .form-group input.error,
        .form-group select.error,
        .form-group textarea.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
        
        .field-error {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .field-error::before {
            content: "⚠";
        }
        
        /* Floating Labels */
        .floating-label {
            position: relative;
        }
        
        .floating-label label {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: white;
            padding: 0 0.5rem;
            transition: all 0.3s ease;
            pointer-events: none;
            color: #6b7280;
        }
        
        .floating-label.focused label,
        .floating-label input:focus + label {
            top: -0.5rem;
            font-size: 0.875rem;
            color: #1e3a8a;
        }
        
        /* Character Counter */
        .character-counter {
            text-align: left;
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
        
        .character-counter.warning {
            color: #f59e0b;
        }
        
        /* Password Toggle */
        .password-wrapper {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.25rem;
        }
        
        .password-toggle:hover {
            color: #1e3a8a;
        }
        
        /* File Upload */
        .file-upload-wrapper {
            position: relative;
        }
        
        .file-upload-wrapper input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-upload-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            border: 2px dashed #d1d5db;
            border-radius: 0.5rem;
            background: #f9fafb;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload-label:hover,
        .file-upload-wrapper.dragover .file-upload-label {
            border-color: #1e3a8a;
            background: #eff6ff;
        }
        
        .file-upload-label.has-file {
            border-color: #059669;
            background: #ecfdf5;
        }
        
        .file-upload-label i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #6b7280;
        }
        
        .file-upload-label.has-file i {
            color: #059669;
        }
        
        /* Multi-Step Form */
        .form-progress {
            margin-bottom: 2rem;
            position: relative;
        }
        
        .progress-fill {
            height: 4px;
            background: #1e3a8a;
            border-radius: 2px;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
        }
        
        .progress-step {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #6b7280;
            transition: all 0.3s ease;
        }
        
        .progress-step.completed {
            background: #1e3a8a;
            color: white;
        }
        
        .form-step {
            display: none;
        }
        
        .form-step.active {
            display: block;
            animation: fadeInUp 0.3s ease;
        }
        
        .form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
            gap: 1rem;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Keyboard Navigation Styles */
        .keyboard-navigation *:focus {
            outline: 2px solid #1e3a8a;
            outline-offset: 2px;
        }
    `;
    
    document.head.appendChild(style);
}

// ===== Export Form Classes =====
window.FormUtils = {
    FormValidator,
    FormEnhancer,
    MultiStepForm,
    formConfig
};