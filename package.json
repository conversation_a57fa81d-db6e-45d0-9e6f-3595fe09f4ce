{"name": "firas-raheem-majiser-campaign-website", "version": "1.0.0", "description": "الموقع الرسمي للأستاذ فراس رحيم مجيسر - مرشح مستقل لخدمة بغداد والعراق", "main": "index.html", "scripts": {"start": "http-server -p 8080 -c-1", "dev": "http-server -p 3000 -c-1 -o", "build": "echo 'Building static site...' && npm run minify", "minify": "npm run minify-css && npm run minify-js", "minify-css": "cleancss -o css/style.min.css css/style.css css/responsive.css", "minify-js": "uglifyjs js/main.js js/animations.js js/forms.js -o js/app.min.js", "serve": "http-server -p 8080", "test": "echo 'No tests specified'", "lint": "eslint js/*.js", "format": "prettier --write '**/*.{html,css,js,json,md}'", "validate": "html-validate index.html", "optimize-images": "imagemin images/**/*.{jpg,png,gif} --out-dir=images/optimized", "deploy": "echo 'Deploying to production...'", "lighthouse": "lighthouse http://localhost:8080 --output html --output-path ./lighthouse-report.html"}, "keywords": ["ر<PERSON>ي<PERSON> مجيسر", "انتخابات", "بغداد", "العراق", "مر<PERSON><PERSON> مستقل", "حملة انتخابية", "موقع سياسي", "rah<PERSON>m ma<PERSON>", "iraq elections", "bag<PERSON><PERSON>", "political campaign"], "author": {"name": "فريق تطوير حملة الأستاذ رحيم مجيسر", "email": "<EMAIL>", "url": "https://raheemmajiser.com"}, "license": "UNLICENSED", "private": true, "homepage": "https://raheemmajiser.com", "repository": {"type": "git", "url": "https://github.com/raheem-majiser/campaign-website.git"}, "bugs": {"url": "https://github.com/raheem-majiser/campaign-website/issues", "email": "<EMAIL>"}, "devDependencies": {"http-server": "^14.1.1", "clean-css-cli": "^5.6.2", "uglify-js": "^3.17.4", "eslint": "^8.57.0", "prettier": "^3.2.5", "html-validate": "^8.9.1", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-gifsicle": "^7.0.0", "lighthouse": "^11.4.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "config": {"port": 8080, "host": "localhost"}, "directories": {"doc": "./docs", "test": "./tests"}, "files": ["index.html", "manifest.json", "sw.js", "css/", "js/", "images/", "videos/", "robots.txt", "sitemap.xml", ".htaccess"]}