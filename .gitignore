# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node_modules
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# parcel-bundler cache (https://parceljs.logs/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
build/
dist/
*.min.js
*.min.css

# Backup files
*.backup
*.bak
*.old
*.orig

# Database files
*.db
*.sqlite
*.sqlite3

# Compressed files
*.zip
*.rar
*.7z
*.tar.gz

# Image optimization outputs
images/optimized/
images/compressed/

# Generated reports
lighthouse-report.html
coverage-report/
test-results/

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Deployment files
deploy.sh
.deploy/

# Analytics and tracking
google-analytics.js
facebook-pixel.js

# Comments and notes
TODO.md
NOTES.md
*.todo

# Sensitive information
config/secrets.js
.secrets
credentials.json

# Large media files (uncomment if needed)
# *.mp4
# *.avi
# *.mov
# *.wmv
# *.flv

# Development tools
.sass-cache/
*.css.map
*.js.map

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Testing
test-results/
screenshots/
videos/test/

# Documentation builds
docs/build/
docs/.vuepress/dist/

# Miscellaneous
*.tgz
*.tar.gz
.cache
.temp